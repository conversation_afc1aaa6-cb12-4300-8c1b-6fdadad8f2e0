<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Location Field Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .coordinates {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <h1>Location Field Test - Option 3: Use Current Location</h1>

    <div class="test-container">
        <h2>Test Current Location Functionality</h2>
        <p>This test simulates the "Use current location" option from the LocationField component.</p>

        <button onclick="testCurrentLocation()">Get Current Location</button>

        <div id="status"></div>
        <div id="coordinates"></div>
        <div id="display-area"></div>
    </div>

    <div class="test-container">
        <h2>Expected Behavior</h2>
        <ol>
            <li>Click "Get Current Location" button</li>
            <li>Browser should prompt for location permission</li>
            <li>Once permission is granted, GPS coordinates should be fetched</li>
            <li><strong>If a close location is found:</strong> Show dialog asking "Are you in [Location Name]?"</li>
            <li><strong>If NO close location is found:</strong> Display GPS coordinates in the Main display area</li>
        </ol>
        <p><strong>Note:</strong> This test simulates the "no close location found" scenario, so coordinates will be
            displayed.</p>
    </div>

    <script>
        function testCurrentLocation() {
            const statusDiv = document.getElementById('status');
            const coordinatesDiv = document.getElementById('coordinates');
            const displayAreaDiv = document.getElementById('display-area');

            statusDiv.innerHTML = '<div class="success">Getting your current location... Please wait while we fetch your location.</div>';

            if ('geolocation' in navigator) {
                const options = {
                    timeout: 30000, // 30 seconds
                };

                navigator.geolocation.getCurrentPosition(
                    ({ coords }) => {
                        const { latitude, longitude } = coords;

                        // Clear status
                        statusDiv.innerHTML = '<div class="success">Location found!</div>';

                        // Display coordinates
                        coordinatesDiv.innerHTML = `
                            <h3>GPS Coordinates:</h3>
                            <div class="coordinates">
                                Latitude: ${latitude.toFixed(6)}<br>
                                Longitude: ${longitude.toFixed(6)}
                            </div>
                        `;

                        // Update main display area (this simulates what should happen in the React component)
                        displayAreaDiv.innerHTML = `
                            <h3>Main Display Area:</h3>
                            <div class="coordinates">
                                ${Number(latitude).toFixed(6)}, ${Number(longitude).toFixed(6)}
                            </div>
                            <p><strong>✅ SUCCESS:</strong> Coordinates are now displayed in the main display area!</p>
                        `;

                        console.log('Location found:', { latitude, longitude });
                    },
                    (error) => {
                        let errorMessage = 'Failed to get current location';
                        if (error.code === error.PERMISSION_DENIED) {
                            errorMessage = 'Location access denied. Please enable location permissions.';
                        } else if (error.code === error.POSITION_UNAVAILABLE) {
                            errorMessage = 'Location information unavailable.';
                        } else if (error.code === error.TIMEOUT) {
                            errorMessage = 'Location request timed out.';
                        }

                        statusDiv.innerHTML = `<div class="error">Error: ${errorMessage}</div>`;
                        coordinatesDiv.innerHTML = '';
                        displayAreaDiv.innerHTML = '';

                        console.error('Geolocation error:', error);
                    },
                    options
                );
            } else {
                statusDiv.innerHTML = '<div class="error">Error: Geolocation is not supported by your browser</div>';
                coordinatesDiv.innerHTML = '';
                displayAreaDiv.innerHTML = '';
            }
        }
    </script>
</body>

</html>
