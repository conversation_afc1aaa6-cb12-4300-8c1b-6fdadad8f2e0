'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Combobox } from '@/components/ui/comboBox'
import {
    CREATE_GEO_LOCATION,
    CreateFavoriteLocation,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'
import { isEmpty, trim } from 'lodash'

import {
    GET_GEO_LOCATIONS,
    GetFavoriteLocations,
} from '@/app/lib/graphQL/query'
import FavoriteLocationModel from '@/app/offline/models/favoriteLocation'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { MapPin, ChevronDown } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDialogNew, P } from '@/components/ui'

export default function LocationField({
    setCurrentLocation,
    handleLocationChange,
    currentEvent,
    offline = false,
    showAddNewLocation = true,
    showUseCoordinates = true,
    showCurrentLocation = true,
}: {
    setCurrentLocation: any
    handleLocationChange: any
    currentEvent?: any
    offline?: boolean
    showAddNewLocation?: boolean
    showUseCoordinates?: boolean
    showCurrentLocation?: boolean
}) {
    const [geoLocations, setGeoLocations] = useState<any>([])
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [newLocation, setNewLocation] = useState(false)
    const [closestLocation, setClosestLocation] = useState<any>(false)
    const [selectedLocation, setSelectedLocation] = useState<any>()
    const [favoriteLocations, setFavoriteLocations] = useState<any>([])
    const { toast } = useToast()
    const [selectedParentLocation, setSelectedParentLocation] =
        useState<any>(false)
    const [locations, setLocations] = useState<any>()
    const [openNewLocationDialog, setOpenNewLocationDialog] =
        useState<boolean>(false)
    const [openSetLocationDialog, setOpenSetLocationDialog] =
        useState<boolean>(false)
    const [location, setLocation] = useState<{
        latitude: any
        longitude: any
    }>({ latitude: 0, longitude: 0 })
    const favoriteLocationModel = new FavoriteLocationModel()
    const geolocationModel = new GeoLocationModel()
    const displayNewLocation = () => {
        setNewLocation(true)
    }

    const hideNewLocation = () => {
        setNewLocation(false)
    }

    const handleSetLocationChange = async (selectedLocation: any) => {
        if (!selectedLocation) {
            // Handle clearing the selection
            setSelectedLocation(null)
            handleLocationChange(null)
            return
        }

        if (selectedLocation.value === 'newLocation') {
            setSelectedParentLocation(false)
            // Instead of opening a separate modal, switch to the "new" option in the current modal
            setLocationOption('new')
        } else {
            setSelectedLocation(selectedLocation)
            handleLocationChange(selectedLocation)
            const userId = localStorage.getItem('userId')
            if (userId !== null && +userId > 0) {
                // if (+localStorage.getItem('userId')> 0) {
                if (offline) {
                    // Check if this location is already a favorite
                    const existingFavorite = favoriteLocations.find(
                        (fav: any) =>
                            fav.geoLocationID === selectedLocation.value,
                    )

                    if (existingFavorite) {
                        // Update usage count for existing favorite
                        const updatedFavorite = {
                            ...existingFavorite,
                            usage: (existingFavorite.usage || 0) + 1,
                        }
                        await favoriteLocationModel.save(updatedFavorite)

                        // Update favoriteLocations state
                        const updatedFavorites = favoriteLocations.map(
                            (fav: any) =>
                                fav.id === existingFavorite.id
                                    ? updatedFavorite
                                    : fav,
                        )
                        setFavoriteLocations(updatedFavorites)
                    } else {
                        // Create new favorite with high usage
                        const newFavorite = {
                            id: generateUniqueId(),
                            memberID: +localStorage.getItem('userId')!,
                            geoLocationID: +selectedLocation.value,
                            usage: 999, // High usage to ensure it appears at the top
                        }
                        await favoriteLocationModel.save(newFavorite)

                        // Update favoriteLocations state
                        setFavoriteLocations([
                            newFavorite,
                            ...favoriteLocations,
                        ])
                    }
                } else {
                    // Check if this location is already a favorite
                    const existingFavorite = favoriteLocations.find(
                        (fav: any) =>
                            fav.geoLocationID === selectedLocation.value,
                    )

                    createFavoriteLocation({
                        variables: {
                            input: {
                                memberID: +localStorage.getItem('userId')!,
                                geoLocationID: +selectedLocation.value,
                            },
                        },
                        onCompleted: (data) => {
                            if (existingFavorite) {
                                // If it was already a favorite, we've just increased its usage count
                                // Refresh the favorites list to get updated usage counts
                                getFavoriteLocations({
                                    variables: {
                                        userID: +localStorage.getItem(
                                            'userId',
                                        )!,
                                    },
                                })
                            } else {
                                // Add the new favorite to the state with high usage
                                const newFavorite = {
                                    id: data.createFavoriteLocation.id,
                                    geoLocationID: selectedLocation.value,
                                    usage: 999, // High usage to ensure it appears at the top
                                }
                                setFavoriteLocations([
                                    newFavorite,
                                    ...favoriteLocations,
                                ])
                            }
                        },
                    })
                }
            }
        }
    }

    const [createFavoriteLocation] = useMutation(CreateFavoriteLocation, {
        onError: (error) => {
            console.error('Error creating favorite location:', error)
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Failed to mark location as favorite',
            })
        },
    })

    const handleParentLocationChange = (selectedLocation: any) => {
        setSelectedParentLocation(selectedLocation)
    }

    const handleCreateNewLocation = async () => {
        const titleInput = document.getElementById(
            'new-location-title',
        ) as HTMLInputElement
        const latitudeInput = document.getElementById(
            'new-location-latitude',
        ) as HTMLInputElement
        const longitudeInput = document.getElementById(
            'new-location-longitude',
        ) as HTMLInputElement

        // Validate inputs exist
        if (!titleInput || !latitudeInput || !longitudeInput) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Could not find all required input fields',
            })
            return
        }

        const title = titleInput.value?.trim()
        const parentLocation = selectedParentLocation
            ? selectedParentLocation.value
            : null
        const latitude = latitudeInput.value?.trim()
        const longitude = longitudeInput.value?.trim()

        // Validate required fields
        if (!title) {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description: 'Location name is required',
            })
            return
        }

        if (!latitude || !longitude) {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description: 'Both latitude and longitude are required',
            })
            return
        }

        // Convert to numbers and validate
        const latNum = parseFloat(latitude)
        const longNum = parseFloat(longitude)

        if (isNaN(latNum) || isNaN(longNum)) {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description: 'Latitude and longitude must be valid numbers',
            })
            return
        }

        const variables = {
            input: {
                title: title,
                lat: latNum,
                long: longNum,
                parentLocationID: parentLocation,
            },
        }

        if (offline) {
            const uniqueID = generateUniqueId()
            const data = await geolocationModel.save({
                ...variables.input,
                id: uniqueID,
            })
            if (locations?.length > 0) {
                setLocations([...locations, data])
            } else {
                setLocations([data])
            }

            // Create location object to use
            const newLocation = {
                label: data.title,
                value: data.id,
                latitude: data.lat,
                longitude: data.long,
            }

            setSelectedLocation(newLocation)
            handleLocationChange(newLocation)

            // Automatically mark newly created location as a favorite
            const userId = localStorage.getItem('userId')
            if (userId !== null && +userId > 0) {
                // Save to favorite locations with high usage to ensure it appears at the top
                const favoriteId = generateUniqueId()
                const newFavorite = {
                    id: favoriteId,
                    memberID: +userId,
                    geoLocationID: data.id,
                    usage: 999, // High usage to ensure it appears at the top
                }

                await favoriteLocationModel.save(newFavorite)

                // Update favoriteLocations state to include the new favorite
                setFavoriteLocations([newFavorite, ...favoriteLocations])
            }

            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
            setOpenLocationModal(false) // Also close the main modal
        } else {
            createGeoLocation({
                variables,
            })
        }
    }

    const [createGeoLocation] = useMutation(CREATE_GEO_LOCATION, {
        onCompleted: (response) => {
            const data = response.createGeoLocation
            if (locations?.length > 0) {
                setLocations([...locations, data])
            } else {
                setLocations([data])
            }

            // Create location object to use
            const newLocation = {
                label: data.title,
                value: data.id,
                latitude: data.lat,
                longitude: data.long,
            }

            setSelectedLocation(newLocation)
            handleLocationChange(newLocation)

            // Automatically mark newly created location as a favorite
            const userId = localStorage.getItem('userId')
            if (userId !== null && +userId > 0) {
                createFavoriteLocation({
                    variables: {
                        input: {
                            memberID: +userId,
                            geoLocationID: +data.id,
                        },
                    },
                    onCompleted: (favData) => {
                        // Update favoriteLocations state to include the new favorite
                        // with high usage to ensure it appears at the top
                        const newFavorite = {
                            id: favData.createFavoriteLocation.id,
                            geoLocationID: data.id,
                            usage: 999, // High usage to ensure it appears at the top
                        }
                        setFavoriteLocations([
                            newFavorite,
                            ...favoriteLocations,
                        ])
                    },
                })
            }

            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
            setOpenLocationModal(false) // Also close the main modal
        },
        onError: (error) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Error creating GeoLocation',
            })
            console.error('Error creating GeoLocation: ' + error.message)
            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
            setOpenLocationModal(false) // Also close the main modal
            console.error('onError', error)
        },
    })
    const offlineGetFavoriteLocations = async () => {
        const locations = await favoriteLocationModel.getByMemberID(
            +localStorage.getItem('userId')!,
        )
        setFavoriteLocations(locations)
    }
    useEffect(() => {
        if (offline) {
            offlineGetFavoriteLocations()
        } else {
            getFavoriteLocations({
                variables: {
                    userID: +localStorage.getItem('userId')!,
                },
            })
        }
    }, [])

    const [getFavoriteLocations] = useLazyQuery(GetFavoriteLocations, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const locations = data.readFavoriteLocations.nodes
            setFavoriteLocations(locations)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const allLocations = () => {
        // Filter function to remove locations with empty or whitespace-only titles
        const filterValidLocations = (locs: any[]) => {
            return locs.filter((loc: any) => !isEmpty(trim(loc.label)))
        }

        let mappedLocations =
            geoLocations?.map((location: any) => ({
                label: location.title,
                value: location.id,
                latitude: location.lat,
                longitude: location.long,
            })) || []

        // Combine locations if needed
        let combinedLocations = []
        if (geoLocations && locations?.length > 0) {
            combinedLocations = [...locations, ...mappedLocations]
        } else {
            combinedLocations = [...mappedLocations]
        }

        // Filter out locations with empty titles before sorting
        const validLocations = filterValidLocations(combinedLocations)

        // Sort the filtered locations
        return sortLocations(validLocations)
    }

    const sortLocations = (locations: any) => {
        // Return empty array if locations is empty or undefined
        if (!locations || locations.length === 0) {
            return []
        }

        // Create a copy of the array to avoid mutating the original
        const locationsToSort = [...locations]

        if (favoriteLocations.length > 0) {
            return locationsToSort.sort((a: any, b: any) => {
                // Find if locations are favorites
                const aFav = favoriteLocations.find(
                    (fav: any) => fav.geoLocationID === a.value,
                )
                const bFav = favoriteLocations.find(
                    (fav: any) => fav.geoLocationID === b.value,
                )

                // Both are favorites - first sort by ID (highest to lowest for newly created locations)
                if (aFav && bFav) {
                    return bFav.usage - aFav.usage
                }
                // Only a is a favorite - a comes first
                else if (aFav) {
                    return -1
                }
                // Only b is a favorite - b comes first
                else if (bFav) {
                    return 1
                }
                return 0
            })
        }

        return locationsToSort
    }

    const getProximity = (location: any, latitude: any, longitude: any) => {
        const distance = Math.sqrt(
            Math.pow(location.lat - latitude, 2) +
                Math.pow(location.long - longitude, 2),
        )
        return distance
    }

    const findClosestLocation = (latitude: any, longitude: any) => {
        // Only try to find closest location if we have locations available
        if (geoLocations.length === 0) {
            setClosestLocation(null)
            return
        }

        const closestLocation = geoLocations.reduce((prev: any, curr: any) => {
            const prevDistance = Math.sqrt(
                Math.pow(prev.lat - latitude, 2) +
                    Math.pow(prev.long - longitude, 2),
            )
            const currDistance = Math.sqrt(
                Math.pow(curr.lat - latitude, 2) +
                    Math.pow(curr.long - longitude, 2),
            )
            return prevDistance < currDistance ? prev : curr
        })

        const proximity = getProximity(closestLocation, latitude, longitude)
        console.log('findClosestLocation', {
            locationOption,
            closestLocation,
            proximity,
        })

        // Only set closest location if it's reasonably close and valid
        if (
            proximity <= 0.15 &&
            closestLocation.lat !== 0 &&
            closestLocation.long !== 0
        ) {
            setClosestLocation({
                label: closestLocation.title,
                value: closestLocation.id,
                latitude: closestLocation.lat,
                longitude: closestLocation.long,
            })
        } else {
            // Clear closest location if none are close enough
            setClosestLocation(null)
        }
    }

    const handleSetCurrentLocation = () => {
        toast({
            title: 'Getting your current location...',
            description: 'Please wait while we fetch your location.',
        })
        if ('geolocation' in navigator) {
            const options = {
                timeout: 30000, // 30 seconds
            }
            navigator.geolocation.getCurrentPosition(
                ({ coords }) => {
                    const { latitude, longitude } = coords
                    setLocation({ latitude, longitude })
                    setCurrentLocation({
                        latitude: +latitude,
                        longitude: +longitude,
                    })

                    // Clear any selected location since we're using coordinates
                    setSelectedLocation(null)

                    // Pass coordinates to parent component immediately
                    handleLocationChange({
                        latitude: +latitude,
                        longitude: +longitude,
                    })

                    // Find closest location for optional suggestion
                    findClosestLocation(latitude, longitude)

                    toast({
                        title: 'Location found!',
                        description: `Coordinates: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                    })
                },
                (error) => {
                    let errorMessage = 'Failed to get current location'
                    if (error.code === error.PERMISSION_DENIED) {
                        errorMessage =
                            'Location access denied. Please enable location permissions.'
                    } else if (error.code === error.POSITION_UNAVAILABLE) {
                        errorMessage = 'Location information unavailable.'
                    } else if (error.code === error.TIMEOUT) {
                        errorMessage = 'Location request timed out.'
                    }

                    toast({
                        variant: 'destructive',
                        title: 'Error',
                        description: errorMessage,
                    })
                },
                options,
            )
        } else {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Geolocation is not supported by your browser',
            })
        }
    }

    const handleSetLocation = () => {
        setSelectedLocation(closestLocation)
        handleLocationChange(closestLocation)
        setOpenSetLocationDialog(false)
    }

    const updateLocationCoordinates = () => {
        const latitude = (
            document.getElementById('location-latitude') as HTMLInputElement
        ).value
        const longitude = (
            document.getElementById('location-longitude') as HTMLInputElement
        ).value

        // Update local state
        setLocation({ latitude: +latitude, longitude: +longitude })

        // Update parent component state
        setCurrentLocation({
            latitude: +latitude,
            longitude: +longitude,
        })

        // Clear selectedLocation when manually entering coordinates
        setSelectedLocation(null)
    }

    const [getGeoLocations] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readGeoLocations.nodes
            if (data) {
                setGeoLocations(data)
            }
        },
        onError: (error: any) => {
            console.error('queryGeoLocations error', error)
        },
    })
    const loadGeoLocations = async () => {
        if (offline) {
            const data = await geolocationModel.getAll()
            if (data) {
                setGeoLocations(data)
            }
        } else {
            await getGeoLocations()
        }
    }

    useEffect(() => {
        if (isLoading) {
            loadGeoLocations()
            setIsLoading(false)
        }
    }, [isLoading])

    useEffect(() => {
        if (currentEvent) {
            if (+currentEvent?.geoLocationID > 0) {
                geoLocations?.find((location: any) => {
                    if (location.id === currentEvent.geoLocationID) {
                        setSelectedLocation({
                            label: location.title,
                            value: location.id,
                            latitude: location.lat,
                            longitude: location.long,
                        })
                    }
                })
                // Clear location coordinates when a location is selected
                setLocation({ latitude: 0, longitude: 0 })
            } else {
                if (currentEvent?.lat && currentEvent?.long) {
                    // Set coordinates and clear selected location
                    setLocation({
                        latitude: currentEvent.lat,
                        longitude: currentEvent.long,
                    })
                    setSelectedLocation(null)
                }
            }
        }
    }, [currentEvent, geoLocations])

    // Initialize form data when location changes
    useEffect(() => {
        // Initialize form data with current location coordinates
        setNewLocationFormData((prev) => ({
            ...prev,
            latitude: location.latitude ? location.latitude.toString() : '',
            longitude: location.longitude ? location.longitude.toString() : '',
        }))

        // Validate the form after a short delay
        setTimeout(validateNewLocationForm, 100)
    }, [location])
    // Create refs for latitude and longitude inputs
    const latInputRef = useRef<HTMLInputElement | null>(null)
    const longInputRef = useRef<HTMLInputElement | null>(null)

    // State for the combined location modal
    const [openLocationModal, setOpenLocationModal] = useState<boolean>(false)
    const [locationOption, setLocationOption] = useState<string>('select')
    // We'll use the validateNewLocationForm function directly instead of storing the state
    const [newLocationFormData, setNewLocationFormData] = useState({
        title: '',
        latitude: '',
        longitude: '',
    })

    // Handle location option change in the modal
    const handleLocationOptionChange = (value: string) => {
        setLocationOption(value)

        // Reset other inputs when changing options
        if (value === 'coordinates') {
            // Show coordinates input
            setNewLocation(false)
            // Clear selectedLocation when switching to coordinates
            setSelectedLocation(null)
        } else {
            if (value === 'current') {
                // Trigger current location detection
                handleSetCurrentLocation()
            } else if (value === 'new') {
                // Show new location form in the current modal
                setNewLocation(true)
                // Don't open the separate modal dialog
                // setOpenNewLocationDialog(true)
            }
        }
    }

    // Get display text for the location field
    const getLocationDisplayText = () => {
        if (selectedLocation?.label) {
            return selectedLocation.label
        } else if (
            // Check if we have valid coordinates to display
            (location.latitude !== undefined &&
                location.latitude !== null &&
                location.latitude !== 0) ||
            (location.longitude !== undefined &&
                location.longitude !== null &&
                location.longitude !== 0)
        ) {
            // Format coordinates with fixed decimal places for better readability
            return `${Number(location.latitude).toFixed(6)}, ${Number(location.longitude).toFixed(6)}`
        } else {
            return 'Select location'
        }
    }

    // Handle the "Use Location" button in the modal
    const handleUseLocation = () => {
        if (locationOption === 'coordinates') {
            // Make sure coordinates are properly set
            hideNewLocation()
            // Clear selectedLocation when using coordinates
            setSelectedLocation(null)
            // Pass coordinates to parent component
            handleLocationChange({
                latitude: location.latitude,
                longitude: location.longitude,
            })
        }
        setOpenLocationModal(false)
    }

    // Validate the new location form
    const validateNewLocationForm = () => {
        const { title, latitude, longitude } = newLocationFormData
        const isValid =
            title.trim() !== '' &&
            latitude.trim() !== '' &&
            longitude.trim() !== '' &&
            !isNaN(parseFloat(latitude)) &&
            !isNaN(parseFloat(longitude))

        return isValid
    }

    // Handle input changes for the new location form
    const handleNewLocationInputChange = (
        e: React.ChangeEvent<HTMLInputElement>,
    ) => {
        const { name, value } = e.target
        setNewLocationFormData((prev) => ({
            ...prev,
            [name]: value,
        }))

        // Validate after a short delay to avoid excessive validation
        setTimeout(validateNewLocationForm, 100)
    }

    // Handle the "Add Location" button in the modal
    const handleAddLocation = () => {
        // Validate form before submitting
        if (validateNewLocationForm()) {
            // Set the input values to match our state before calling handleCreateNewLocation
            const titleInput = document.getElementById(
                'new-location-title',
            ) as HTMLInputElement
            const latInput = document.getElementById(
                'new-location-latitude',
            ) as HTMLInputElement
            const longInput = document.getElementById(
                'new-location-longitude',
            ) as HTMLInputElement

            if (titleInput && latInput && longInput) {
                titleInput.value = newLocationFormData.title
                latInput.value = newLocationFormData.latitude
                longInput.value = newLocationFormData.longitude
            }

            handleCreateNewLocation()
            setOpenLocationModal(false)
        } else {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description:
                    'Please fill in all required fields with valid values',
            })
        }
    }

    // Override the existing handlers to close the modal
    const originalHandleSetLocationChange = handleSetLocationChange
    const handleSetLocationChangeWithModal = (selectedLocation: any) => {
        originalHandleSetLocationChange(selectedLocation)
        if (selectedLocation && selectedLocation.value !== 'newLocation') {
            setOpenLocationModal(false)
        }
    }

    return (
        <>
            {/* Main display area with location and button */}
            <Button
                iconRight={<ChevronDown className="text-neutral-400" />}
                className="w-full justify-between max-h-11 text-base"
                variant="outline"
                onClick={() => {
                    setLocationOption('select')
                    setOpenLocationModal(true)
                }}>
                {getLocationDisplayText()}
            </Button>

            {/* Combined Location Modal */}
            <AlertDialogNew
                openDialog={openLocationModal}
                setOpenDialog={setOpenLocationModal}
                handleCreate={() => {
                    if (locationOption === 'coordinates') {
                        handleUseLocation()
                    } else if (locationOption === 'current') {
                        // For current location, just close the modal since coordinates are already set
                        setOpenLocationModal(false)
                    } else if (locationOption === 'new') {
                        // Only call handleAddLocation if the form is valid
                        if (validateNewLocationForm()) {
                            handleAddLocation()
                        } else {
                            toast({
                                variant: 'destructive',
                                title: 'Validation Error',
                                description:
                                    'Please fill in all required fields with valid values',
                            })
                            // Return without closing the modal
                            return
                        }
                    } else {
                        // Default case for 'select' option
                        setOpenLocationModal(false)
                    }
                }}
                actionText={
                    locationOption === 'coordinates'
                        ? 'Use Location'
                        : locationOption === 'current'
                          ? 'Use Current Location'
                          : locationOption === 'new'
                            ? 'Add Location'
                            : 'OK'
                }
                title="Select location"
                size="md">
                <div className="flex flex-col gap-5">
                    {/* Radio options for location selection method */}
                    <RadioGroup
                        value={locationOption}
                        onValueChange={handleLocationOptionChange}
                        className="space-y-8">
                        {/* Option 1: Select from dropdown */}
                        <div className="w-full flex gap-2.5">
                            <RadioGroupItem
                                size="md"
                                value="select"
                                id="select-location"
                            />
                            <Label
                                className="text-base w-full font-medium"
                                label="Select from locations"
                                htmlFor="select-location">
                                {locationOption === 'select' && (
                                    <div className="flex flex-row w-full">
                                        <Combobox
                                            options={
                                                geoLocations
                                                    ? [
                                                          {
                                                              label: ' Add New Location',
                                                              value: 'newLocation',
                                                          },
                                                          ...allLocations(),
                                                      ]
                                                    : [
                                                          {
                                                              label: ' Add New Location',
                                                              value: 'newLocation',
                                                          },
                                                      ]
                                            }
                                            value={selectedLocation}
                                            onChange={
                                                handleSetLocationChangeWithModal
                                            }
                                            placeholder="Select location"
                                        />
                                    </div>
                                )}
                            </Label>
                        </div>

                        {/* Option 2: Enter coordinates */}
                        {showUseCoordinates && (
                            <div className="flex gap-2.5">
                                <RadioGroupItem
                                    size="md"
                                    value="coordinates"
                                    id="enter-coordinates"
                                />
                                <Label
                                    label="Enter coordinates"
                                    htmlFor="enter-coordinates"
                                    className="text-base font-medium">
                                    {locationOption === 'coordinates' && (
                                        <div className="flex flex-col gap-4 w-full p-4 border border-neutral-400 border-dashed rounded-lg bg-card">
                                            <div className="flex flex-col gap-4 w-full">
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <Label
                                                        label="Latitude"
                                                        htmlFor="location-latitude">
                                                        <Input
                                                            id="location-latitude"
                                                            name="latitude"
                                                            type="number"
                                                            ref={latInputRef}
                                                            value={
                                                                location.latitude
                                                            }
                                                            onChange={
                                                                updateLocationCoordinates
                                                            }
                                                            placeholder="Enter latitude"
                                                            className="w-full"
                                                            required
                                                        />
                                                    </Label>

                                                    <Label
                                                        label="Longitude"
                                                        htmlFor="location-longitude">
                                                        <Input
                                                            id="location-longitude"
                                                            name="longitude"
                                                            type="number"
                                                            ref={longInputRef}
                                                            value={
                                                                location.longitude
                                                            }
                                                            onChange={
                                                                updateLocationCoordinates
                                                            }
                                                            placeholder="Enter longitude"
                                                            className="w-full"
                                                            required
                                                        />
                                                    </Label>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="text-sm text-muted-foreground italic">
                                                        Enter coordinates in
                                                        decimal degrees format
                                                        (e.g., 37.80255,
                                                        -122.41463)
                                                    </p>
                                                </div>
                                                {(location.latitude !== 0 ||
                                                    location.longitude !==
                                                        0) && (
                                                    <div className="mt-2 p-3 bg-muted rounded-md">
                                                        <p>
                                                            Current coordinates:{' '}
                                                            {Number(
                                                                location.latitude,
                                                            ).toFixed(6)}
                                                            ,{' '}
                                                            {Number(
                                                                location.longitude,
                                                            ).toFixed(6)}
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </Label>
                            </div>
                        )}

                        {/* Option 3: Use current location */}
                        {showCurrentLocation && (
                            <div className="flex gap-2.5">
                                <RadioGroupItem
                                    size="md"
                                    value="current"
                                    id="current-location"
                                />
                                <Label
                                    label="Use current location"
                                    htmlFor="current-location"
                                    className="text-base font-medium">
                                    <div>
                                        <p className="text-sm text-muted-foreground">
                                            Uses your GPS coordinates as the
                                            location.
                                        </p>
                                        {locationOption === 'current' && (
                                            <div className="mt-2">
                                                {/* Show current coordinates if available */}
                                                {(location.latitude !== 0 ||
                                                    location.longitude !==
                                                        0) && (
                                                    <div className="p-4 border rounded-lg border-neutral-400 border-dashed bg-card mb-2">
                                                        <P className="mb-2 font-medium">
                                                            Current coordinates:
                                                        </P>
                                                        <P className="text-sm font-mono">
                                                            {Number(
                                                                location.latitude,
                                                            ).toFixed(6)}
                                                            ,{' '}
                                                            {Number(
                                                                location.longitude,
                                                            ).toFixed(6)}
                                                        </P>
                                                    </div>
                                                )}

                                                {/* Show closest location suggestion if available */}
                                                {closestLocation?.label && (
                                                    <div className="p-4 border rounded-lg border-neutral-400 border-dashed bg-card">
                                                        <P className="mb-2">
                                                            Nearest location:{' '}
                                                            {
                                                                closestLocation?.label
                                                            }
                                                        </P>
                                                        <Button
                                                            iconLeft={MapPin}
                                                            onClick={
                                                                handleSetLocation
                                                            }
                                                            variant="outline"
                                                            className="w-full">
                                                            Use{' '}
                                                            {
                                                                closestLocation?.label
                                                            }{' '}
                                                            instead
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </Label>
                            </div>
                        )}

                        {/* Option 4: Add new location */}
                        {showAddNewLocation && (
                            <div className="w-full flex gap-2.5">
                                <RadioGroupItem
                                    size="md"
                                    value="new"
                                    id="new-location"
                                />
                                <Label
                                    label="Add new location"
                                    htmlFor="new-location"
                                    className="text-base w-full font-medium">
                                    {locationOption === 'new' && (
                                        <div className="flex-grow flex flex-col gap-5 p-4 bg-background rounded-lg border-neutral-400 border border-dashed">
                                            <div className="grid grid-cols-1 gap-5">
                                                <Label
                                                    label="Location Name"
                                                    htmlFor="new-location-title">
                                                    <Input
                                                        id="new-location-title"
                                                        name="title"
                                                        type="text"
                                                        placeholder="Enter location name"
                                                        value={
                                                            newLocationFormData.title
                                                        }
                                                        onChange={
                                                            handleNewLocationInputChange
                                                        }
                                                        required
                                                    />
                                                </Label>

                                                {/* <Label
                                                    label="Parent Location (Optional)"
                                                    className="w-full"
                                                    htmlFor="parent-location">
                                                    <div className="flex flex-row w-full">
                                                        <Combobox
                                                            options={
                                                                geoLocations
                                                                    ? allLocations()
                                                                    : []
                                                            }
                                                            onChange={
                                                                handleParentLocationChange
                                                            }
                                                            placeholder="Select parent location"
                                                        />
                                                    </div>
                                                </Label> */}
                                            </div>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <Label
                                                    label="Latitude"
                                                    htmlFor="new-location-latitude">
                                                    <Input
                                                        id="new-location-latitude"
                                                        name="latitude"
                                                        type="number"
                                                        value={
                                                            newLocationFormData.latitude ||
                                                            location.latitude
                                                        }
                                                        onChange={
                                                            handleNewLocationInputChange
                                                        }
                                                        placeholder="Enter latitude"
                                                        required
                                                    />
                                                </Label>
                                                <Label
                                                    label="Longitude"
                                                    htmlFor="new-location-longitude">
                                                    <Input
                                                        id="new-location-longitude"
                                                        name="longitude"
                                                        type="number"
                                                        value={
                                                            newLocationFormData.longitude ||
                                                            location.longitude
                                                        }
                                                        onChange={
                                                            handleNewLocationInputChange
                                                        }
                                                        placeholder="Enter longitude"
                                                        required
                                                    />
                                                </Label>
                                            </div>
                                        </div>
                                    )}
                                </Label>
                            </div>
                        )}
                    </RadioGroup>
                </div>
            </AlertDialogNew>

            {/* Keep the original dialogs for compatibility with existing code */}
            <AlertDialogNew
                openDialog={openNewLocationDialog}
                setOpenDialog={setOpenNewLocationDialog}
                handleCreate={handleCreateNewLocation}
                actionText="Add Location"
                title="Add New Location"
                showIcon={true}>
                <div className="flex flex-col gap-5">
                    <div className="grid grid-cols-1 gap-5">
                        <Label
                            label="Location Name"
                            htmlFor="new-location-title">
                            <Input
                                id="new-location-title"
                                name="title"
                                type="text"
                                placeholder="Enter location name"
                                value={newLocationFormData.title}
                                onChange={handleNewLocationInputChange}
                                required
                            />
                        </Label>
                        {/* <Label
                            className="w-full"
                            label="Parent Location (Optional)"
                            htmlFor="parent-location">
                            <div className="flex flex-row w-full">
                                <Combobox
                                    options={geoLocations ? allLocations() : []}
                                    onChange={handleParentLocationChange}
                                    placeholder="Select parent location"
                                />
                            </div>
                        </Label> */}
                        <Label htmlFor="new-location-latitude" label="Latitude">
                            <Input
                                id="new-location-latitude"
                                name="latitude"
                                type="number"
                                value={
                                    newLocationFormData.latitude ||
                                    location.latitude
                                }
                                onChange={handleNewLocationInputChange}
                                placeholder="Enter latitude"
                                required
                            />
                        </Label>
                        <Label
                            label="Longitude"
                            htmlFor="new-location-longitude">
                            <Input
                                id="new-location-longitude"
                                name="longitude"
                                type="number"
                                value={
                                    newLocationFormData.longitude ||
                                    location.longitude
                                }
                                onChange={handleNewLocationInputChange}
                                placeholder="Enter longitude"
                                required
                            />
                        </Label>
                    </div>
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openSetLocationDialog}
                setOpenDialog={setOpenSetLocationDialog}
                handleCreate={handleCreateNewLocation}
                actionText="Create Location"
                title="Current Location">
                <div className="flex flex-col gap-5">
                    {closestLocation?.label ? (
                        <div className="flex flex-col gap-4">
                            {closestLocation?.label != undefined ? (
                                <div className="flex flex-col gap-4">
                                    <P className="font-medium">
                                        Are you in {closestLocation?.label}?
                                    </P>
                                    <Button
                                        iconLeft={MapPin}
                                        onClick={handleSetLocation}
                                        className="w-full">
                                        Use {closestLocation?.label}
                                    </Button>
                                    <Separator className="my-4" />
                                    <P className="uppercase">
                                        Or create new location
                                    </P>
                                </div>
                            ) : (
                                <p className="text-lg">
                                    Fetching current location took long, do you
                                    want to create a new location instead?
                                </p>
                            )}
                        </div>
                    ) : (
                        <p className="text-lg">
                            Failed to fetch current location. Do you want to
                            create a new location instead?
                        </p>
                    )}

                    <div className="grid grid-cols-1 gap-5">
                        <Label
                            label="Location Name"
                            htmlFor="new-location-title">
                            <Input
                                id="new-location-title"
                                name="title"
                                type="text"
                                placeholder="Enter location name"
                                value={newLocationFormData.title}
                                onChange={handleNewLocationInputChange}
                                required
                            />
                        </Label>
                        {/* <Label
                            className="w-full"
                            label="Parent Location (Optional)"
                            htmlFor="parent-location">
                            <div className="flex flex-row w-full">
                                <Combobox
                                    options={geoLocations ? allLocations() : []}
                                    onChange={handleParentLocationChange}
                                    placeholder="Select parent location"
                                />
                            </div>
                        </Label> */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Label
                                label="Latitude"
                                htmlFor="new-location-latitude">
                                <Input
                                    id="new-location-latitude"
                                    name="latitude"
                                    type="number"
                                    value={
                                        newLocationFormData.latitude ||
                                        location.latitude
                                    }
                                    onChange={handleNewLocationInputChange}
                                    placeholder="Enter latitude"
                                    required
                                />
                            </Label>
                            <Label
                                label="Longitude"
                                htmlFor="new-location-longitude">
                                <Input
                                    id="new-location-longitude"
                                    name="longitude"
                                    type="number"
                                    value={
                                        newLocationFormData.longitude ||
                                        location.longitude
                                    }
                                    onChange={handleNewLocationInputChange}
                                    placeholder="Enter longitude"
                                    required
                                />
                            </Label>
                        </div>
                    </div>
                </div>
            </AlertDialogNew>
        </>
    )
}
